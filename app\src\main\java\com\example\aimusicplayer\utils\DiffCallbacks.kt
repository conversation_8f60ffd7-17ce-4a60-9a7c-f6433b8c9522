package com.example.aimusicplayer.utils

import androidx.media3.common.MediaItem
import androidx.recyclerview.widget.DiffUtil
import com.example.aimusicplayer.data.model.Comment
import com.example.aimusicplayer.data.model.PlayList
import com.example.aimusicplayer.model.TopListResponse

/**
 * DiffCallbacks工具类
 * 提供各种列表差异比较回调，用于优化RecyclerView更新
 */
object DiffCallbacks {

    /**
     * 评论列表差异比较回调
     * 用于优化评论列表的更新，减少不必要的重绘
     */
    class CommentDiffCallback(
        private val oldComments: List<Comment>,
        private val newComments: List<Comment>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldComments.size

        override fun getNewListSize(): Int = newComments.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较评论ID是否相同
            return oldComments[oldItemPosition].commentId == newComments[newItemPosition].commentId
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较评论内容是否相同
            val oldComment = oldComments[oldItemPosition]
            val newComment = newComments[newItemPosition]
            return oldComment.content == newComment.content &&
                   oldComment.user?.nickname == newComment.user?.nickname &&
                   oldComment.likedCount == newComment.likedCount
        }
    }

    /**
     * 播放列表差异比较回调（TopListResponse.PlayList版本）
     * 用于优化播放列表的更新，减少不必要的重绘
     */
    class PlaylistDiffCallback(
        private val oldPlaylists: List<TopListResponse.PlayList>,
        private val newPlaylists: List<TopListResponse.PlayList>
    ) : DiffUtil.Callback() {

        override fun getOldListSize(): Int = oldPlaylists.size

        override fun getNewListSize(): Int = newPlaylists.size

        override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较播放列表ID是否相同
            return oldPlaylists[oldItemPosition].id == newPlaylists[newItemPosition].id
        }

        override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
            // 比较播放列表内容是否相同
            val oldPlaylist = oldPlaylists[oldItemPosition]
            val newPlaylist = newPlaylists[newItemPosition]
            return oldPlaylist.name == newPlaylist.name &&
                   oldPlaylist.coverImgUrl == newPlaylist.coverImgUrl &&
                   oldPlaylist.playCount == newPlaylist.playCount
        }
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldComment = oldComments[oldItemPosition]
        val newComment = newComments[newItemPosition]

        // 比较评论内容、点赞数和点赞状态
        val contentSame = oldComment.content == newComment.content
        val likeCountSame = oldComment.likeCount == newComment.likeCount
        val likedSame = oldComment.liked == newComment.liked

        return contentSame && likeCountSame && likedSame
    }
}

/**
 * 媒体项列表差异比较回调
 * 用于优化播放列表的更新，减少不必要的重绘
 */
class MediaItemDiffCallback(
    private val oldMediaItems: List<MediaItem>,
    private val newMediaItems: List<MediaItem>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldMediaItems.size

    override fun getNewListSize(): Int = newMediaItems.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 比较媒体项ID是否相同
        val oldItem = oldMediaItems[oldItemPosition]
        val newItem = newMediaItems[newItemPosition]
        return oldItem.mediaId != null && newItem.mediaId != null &&
               oldItem.mediaId == newItem.mediaId
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldMediaItems[oldItemPosition]
        val newItem = newMediaItems[newItemPosition]

        // 比较标题、艺术家和专辑
        val titleSame = oldItem.mediaMetadata.title == newItem.mediaMetadata.title
        val artistSame = oldItem.mediaMetadata.artist == newItem.mediaMetadata.artist
        val albumSame = oldItem.mediaMetadata.albumTitle == newItem.mediaMetadata.albumTitle

        return titleSame && artistSame && albumSame
    }
}

/**
 * 播放列表差异比较回调
 * 用于优化播放列表的更新，减少不必要的重绘
 */
class PlaylistDiffCallback(
    private val oldPlaylists: List<PlayList>,
    private val newPlaylists: List<PlayList>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldPlaylists.size

    override fun getNewListSize(): Int = newPlaylists.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        // 比较播放列表ID是否相同
        return oldPlaylists[oldItemPosition].id == newPlaylists[newItemPosition].id
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldPlaylists[oldItemPosition]
        val newItem = newPlaylists[newItemPosition]

        // 比较名称、描述和封面URL
        val nameSame = oldItem.name == newItem.name
        val descSame = oldItem.description == newItem.description
        val coverSame = oldItem.coverImgUrl == newItem.coverImgUrl

        return nameSame && descSame && coverSame
    }
}

/**
 * 简化版的差异比较回调基类
 * 用于优化RecyclerView列表的更新，减少不必要的重绘
 * @param T 列表项的类型
 * @property oldItems 旧的列表
 * @property newItems 新的列表
 */
abstract class SimpleDiffCallback<T>(
    private val oldItems: List<T>,
    private val newItems: List<T>
) : DiffUtil.Callback() {

    override fun getOldListSize(): Int = oldItems.size

    override fun getNewListSize(): Int = newItems.size

    override fun areItemsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldItems[oldItemPosition]
        val newItem = newItems[newItemPosition]
        return areItemsTheSameImpl(oldItem, newItem)
    }

    override fun areContentsTheSame(oldItemPosition: Int, newItemPosition: Int): Boolean {
        val oldItem = oldItems[oldItemPosition]
        val newItem = newItems[newItemPosition]
        return areContentsTheSameImpl(oldItem, newItem)
    }

    /**
     * 判断两个项是否是同一个项
     * @param oldItem 旧项
     * @param newItem 新项
     * @return 如果是同一个项，返回true；否则返回false
     */
    protected abstract fun areItemsTheSameImpl(oldItem: T, newItem: T): Boolean

    /**
     * 判断两个项的内容是否相同
     * @param oldItem 旧项
     * @param newItem 新项
     * @return 如果内容相同，返回true；否则返回false
     */
    protected abstract fun areContentsTheSameImpl(oldItem: T, newItem: T): Boolean
}
